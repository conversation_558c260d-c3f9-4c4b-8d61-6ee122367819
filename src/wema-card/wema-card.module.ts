import { Module } from '@nestjs/common';
import { BullModule } from '@nestjs/bullmq';
import { WemaCardService } from './wema-card.service';
import { WemaAccountController } from './wema-card.controller';
import { WemaCardsController } from './wema-cards.controller';
import { WemaAccountRepository } from './repository/wema.account.repository';
import { AddressRepository } from './repository/address.repository';
import { WemaModule } from '../../libs/wema/src';
import { WemaCardRepository } from './repository/wema-card.repository';
import { WemaFundTransactionRepository } from './repository/wema.fund.transactions';
import { PaymentCacheModule } from '@crednet/utils';
import { Events } from '../utils/enums';
import { WemaCardVirtualCardCreationWebhookConsumer } from './consumers/wema-card.virtual-card.creation.webhook.consumer';
import { WemaExternalTransactionRepository } from './repository/wema.external.transactions';

@Module({
  imports: [
    WemaModule,
    PaymentCacheModule,
    BullModule.registerQueue({
      name: Events.REQUERY_FUND_WEMA_CARD,
    }),
  ],
  controllers: [WemaAccountController, WemaCardsController],
  providers: [
    WemaCardService,
    WemaAccountRepository,
    AddressRepository,
    WemaCardRepository,
    WemaFundTransactionRepository,
    WemaCardVirtualCardCreationWebhookConsumer,
    WemaExternalTransactionRepository,
  ],
  exports: [WemaCardService],
})
export class WemaCardModule {}
